/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，从原app.js中提取并重构请求队列管理器
 * 
 * 请求队列管理器 - 管理API请求的并发和频率控制
 * 避免API请求过载，提供智能重试机制
 */

import { BaseService } from '../core/BaseService.js';

class RequestQueue extends BaseService {
    constructor(options = {}) {
        super('RequestQueue');
        
        // 从配置管理器获取默认配置
        const defaultConfig = this.config.get('requestQueue');
        
        this.maxConcurrent = options.maxConcurrent || defaultConfig.maxConcurrent;
        this.requestInterval = options.requestInterval || defaultConfig.requestInterval;
        
        this.queue = [];
        this.running = 0;
        this.lastRequestTime = 0;
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            retriedRequests: 0
        };
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        this.logger.info(`请求队列初始化 - 最大并发: ${this.maxConcurrent}, 请求间隔: ${this.requestInterval}ms`);
        this.resetStats();
    }

    /**
     * 将请求加入队列
     * @param {Function} requestFn - 请求函数
     * @param {Object} options - 请求选项
     * @param {number} options.priority - 优先级 (数字越小优先级越高)
     * @param {number} options.timeout - 超时时间
     * @param {number} options.maxRetries - 最大重试次数
     * @returns {Promise} 请求结果
     */
    enqueue(requestFn, options = {}) {
        return new Promise((resolve, reject) => {
            const requestItem = {
                requestFn,
                resolve,
                reject,
                priority: options.priority || 0,
                timeout: options.timeout || this.config.get('api.timeout', 10000),
                maxRetries: options.maxRetries || this.config.get('api.retryCount', 3),
                retryCount: 0,
                createdAt: Date.now()
            };

            // 按优先级插入队列
            this.insertByPriority(requestItem);
            this.stats.totalRequests++;
            
            this.emit('requestQueued', {
                queueLength: this.queue.length,
                running: this.running
            });

            this.processQueue();
        });
    }

    /**
     * 按优先级插入请求到队列
     * @param {Object} requestItem - 请求项
     */
    insertByPriority(requestItem) {
        let insertIndex = this.queue.length;
        
        for (let i = 0; i < this.queue.length; i++) {
            if (requestItem.priority < this.queue[i].priority) {
                insertIndex = i;
                break;
            }
        }
        
        this.queue.splice(insertIndex, 0, requestItem);
    }

    /**
     * 处理队列中的请求
     */
    async processQueue() {
        // 检查是否可以处理更多请求
        if (this.running >= this.maxConcurrent || this.queue.length === 0) {
            return;
        }

        // 检查请求间隔
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.requestInterval) {
            setTimeout(() => this.processQueue(), this.requestInterval - timeSinceLastRequest);
            return;
        }

        const requestItem = this.queue.shift();
        this.running++;
        this.lastRequestTime = now;

        this.emit('requestStarted', {
            queueLength: this.queue.length,
            running: this.running
        });

        try {
            const result = await this.executeRequest(requestItem);
            requestItem.resolve(result);
            this.stats.successfulRequests++;
            
            this.emit('requestCompleted', {
                success: true,
                queueLength: this.queue.length,
                running: this.running - 1
            });
        } catch (error) {
            // 检查是否需要重试
            if (requestItem.retryCount < requestItem.maxRetries && this.shouldRetry(error)) {
                requestItem.retryCount++;
                this.stats.retriedRequests++;
                
                this.logger.warn(`请求重试 (${requestItem.retryCount}/${requestItem.maxRetries}):`, error.message);
                
                // 重新加入队列，降低优先级
                requestItem.priority += 10;
                this.insertByPriority(requestItem);
                
                this.emit('requestRetried', {
                    retryCount: requestItem.retryCount,
                    maxRetries: requestItem.maxRetries,
                    error: error.message
                });
            } else {
                requestItem.reject(error);
                this.stats.failedRequests++;
                
                this.emit('requestFailed', {
                    error: error.message,
                    retryCount: requestItem.retryCount
                });
            }
        } finally {
            this.running--;
            
            // 继续处理队列
            if (this.queue.length > 0) {
                setTimeout(() => this.processQueue(), this.requestInterval);
            }
        }
    }

    /**
     * 执行单个请求
     * @param {Object} requestItem - 请求项
     * @returns {Promise} 请求结果
     */
    async executeRequest(requestItem) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, requestItem.timeout);

        try {
            const result = await requestItem.requestFn(controller.signal);
            clearTimeout(timeoutId);
            return result;
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            
            throw error;
        }
    }

    /**
     * 判断是否应该重试请求
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否应该重试
     */
    shouldRetry(error) {
        // 网络错误或超时错误可以重试
        const retryableErrors = [
            'NetworkError',
            'TimeoutError',
            '请求超时',
            'Failed to fetch'
        ];

        return retryableErrors.some(retryableError => 
            error.message.includes(retryableError) || error.name === retryableError
        );
    }

    /**
     * 清空队列
     */
    clear() {
        const cancelledCount = this.queue.length;
        
        // 拒绝所有待处理的请求
        this.queue.forEach(item => {
            item.reject(new Error('请求队列已清空'));
        });
        
        this.queue = [];
        
        this.logger.info(`已清空请求队列，取消了 ${cancelledCount} 个待处理请求`);
        
        this.emit('queueCleared', { cancelledCount });
    }

    /**
     * 重置队列
     */
    reset() {
        this.clear();
        this.running = 0;
        this.lastRequestTime = 0;
        this.resetStats();
        
        this.logger.info('请求队列已重置');
        this.emit('queueReset');
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            retriedRequests: 0
        };
    }

    /**
     * 获取队列状态
     * @returns {Object} 队列状态信息
     */
    getStatus() {
        return {
            ...super.getStatus(),
            queueLength: this.queue.length,
            running: this.running,
            maxConcurrent: this.maxConcurrent,
            requestInterval: this.requestInterval,
            stats: { ...this.stats },
            successRate: this.stats.totalRequests > 0 
                ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        if (newConfig.maxConcurrent !== undefined) {
            this.maxConcurrent = newConfig.maxConcurrent;
        }
        
        if (newConfig.requestInterval !== undefined) {
            this.requestInterval = newConfig.requestInterval;
        }
        
        this.logger.info('请求队列配置已更新:', newConfig);
        this.emit('configUpdated', newConfig);
    }

    /**
     * 销毁服务
     */
    async onDestroy() {
        this.clear();
        this.logger.info('请求队列服务已销毁');
    }
}

export { RequestQueue };
