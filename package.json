{"name": "geocoding-tool", "version": "0.5.0", "description": "经纬度逆向工具 - 将Excel文件中的经纬度坐标转换为省市区信息", "main": "app.js", "scripts": {"start": "python server.py", "dev": "python server.py", "build": "node build.js", "clean": "node -e \"const fs=require('fs');const path=require('path');function rmDir(dir){if(fs.existsSync(dir)){fs.rmSync(dir,{recursive:true,force:true});console.log('🗑️ 已清理:',dir)}else{console.log('📁 目录不存在:',dir)}};rmDir('dist');\"", "rebuild": "npm run clean && npm run build", "deploy:gh-pages": "npm run build && gh-pages -d dist"}, "keywords": ["geocoding", "excel", "map", "coordinates"], "author": "", "license": "MIT", "devDependencies": {"gh-pages": "^6.0.0"}}