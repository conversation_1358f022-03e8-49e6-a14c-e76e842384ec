// 全局变量
let workbook = null;
let worksheet = null;
let headers = [];
let data = [];
let resultData = [];
let longitudeColIndex = -1;
let latitudeColIndex = -1;
let processStats = { success: 0, failure: 0, skipped: 0 }; // 添加处理统计全局变量

// 本地存储键名
const API_KEY_STORAGE_KEY = 'geoApiKey';

// 高德地图API URL
const GEO_URL = "https://restapi.amap.com/v3/geocode/regeo";

// 请求队列管理器
const RequestQueue = {
    maxConcurrent: 3,
    requestInterval: 300,
    queue: [],
    running: 0,
    lastRequestTime: 0,
    
    enqueue(requestFn) {
        return new Promise((resolve, reject) => {
            this.queue.push({ requestFn, resolve, reject });
            this.processQueue();
        });
    },
    
    async processQueue() {
        if (this.running >= this.maxConcurrent || this.queue.length === 0) return;
        
        const { requestFn, resolve, reject } = this.queue.shift();
        this.running++;
        
        try {
            const now = Date.now();
            const timeToWait = Math.max(0, this.requestInterval - (now - this.lastRequestTime));
            if (timeToWait > 0) await new Promise(r => setTimeout(r, timeToWait));
            
            this.lastRequestTime = Date.now();
            
            const result = await requestFn();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            this.processQueue();
        }
    },
    
    reset() {
        this.queue = [];
        this.running = 0;
        this.lastRequestTime = 0;
    }
};

// DOM元素
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const apiKeyInput = document.getElementById('apiKey');
const toggleApiKeyBtn = document.getElementById('toggleApiKey');
const eyeIcon = document.getElementById('eyeIcon');
const eyeOffIcon = document.getElementById('eyeOffIcon');
const saveApiKeyCheckbox = document.getElementById('saveApiKey');
const nextBtn = document.getElementById('nextBtn');
const backBtn = document.getElementById('backBtn');
const processBtn = document.getElementById('processBtn');
const newFileBtn = document.getElementById('newFileBtn');
const downloadBtn = document.getElementById('downloadBtn');
const testModeCheckbox = document.getElementById('testMode');
const longitudeColSelect = document.getElementById('longitudeCol');
const latitudeColSelect = document.getElementById('latitudeCol');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const processingStatus = document.getElementById('processingStatus');
const resultSummary = document.getElementById('resultSummary');

// 步骤元素
const step1 = document.getElementById('step1');
const step2 = document.getElementById('step2');
const step3 = document.getElementById('step3');
const processing = document.getElementById('processing');
const step1Indicator = document.getElementById('step1-indicator');
const step2Indicator = document.getElementById('step2-indicator');
const step3Indicator = document.getElementById('step3-indicator');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查localStorage是否可用
    checkLocalStorageAvailability();
    
    // 文件选择事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 按钮事件
    nextBtn.addEventListener('click', goToStep2);
    backBtn.addEventListener('click', goToStep1);
    processBtn.addEventListener('click', startProcessing);
    newFileBtn.addEventListener('click', resetApp);
    downloadBtn.addEventListener('click', downloadResult);
    
    // API Key输入事件
    apiKeyInput.addEventListener('input', handleApiKeyInput);
    
    // API Key保存选项事件
    saveApiKeyCheckbox.addEventListener('change', handleSaveApiKeyChange);
    
    // API Key显示/隐藏切换
    toggleApiKeyBtn.addEventListener('click', toggleApiKeyVisibility);
    
    // 拖拽文件功能
    setupDragAndDrop();
    
    // 从本地存储加载API Key
    loadApiKeyFromStorage();
});

// 检查localStorage是否可用
function checkLocalStorageAvailability() {
    try {
        const testKey = '__test_storage__';
        localStorage.setItem(testKey, testKey);
        const result = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
        const isAvailable = result === testKey;
        
        if (!isAvailable) {
            // 禁用保存选项
            saveApiKeyCheckbox.disabled = true;
            saveApiKeyCheckbox.checked = false;
            
            // 添加提示
            const saveApiKeyLabel = document.querySelector('label[for="saveApiKey"]');
            if (saveApiKeyLabel) {
                saveApiKeyLabel.textContent = '保存API Key功能不可用（浏览器不支持或已禁用存储）';
                saveApiKeyLabel.classList.add('text-red-500');
            }
        }
        
        return isAvailable;
    } catch (error) {
        return false;
    }
}

// 从本地存储加载API Key
function loadApiKeyFromStorage() {
    try {
        const savedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
        
        if (savedApiKey) {
            apiKeyInput.value = savedApiKey;
            saveApiKeyCheckbox.checked = true;
            validateForm();
        } else {
            saveApiKeyCheckbox.checked = false;
        }
    } catch (error) {
        // 静默处理错误
    }
}

// 处理API Key输入
function handleApiKeyInput() {
    validateForm();
    // 保存API Key到本地存储
    try {
        const apiKey = apiKeyInput.value.trim();
        
        if (apiKey && saveApiKeyCheckbox.checked) {
            localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
            showSaveStatus(true);
        } else {
            localStorage.removeItem(API_KEY_STORAGE_KEY);
            if (apiKey) {
                showSaveStatus(false);
            }
        }
    } catch (error) {
        showSaveStatus(false, error.message);
    }
}

// 处理保存API Key选项变化
function handleSaveApiKeyChange() {
    try {
        const apiKey = apiKeyInput.value.trim();
        
        if (saveApiKeyCheckbox.checked && apiKey) {
            localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
            showSaveStatus(true);
        } else {
            localStorage.removeItem(API_KEY_STORAGE_KEY);
            if (apiKey) {
                showSaveStatus(false);
            }
        }
    } catch (error) {
        showSaveStatus(false, error.message);
    }
}

// 显示保存状态提示
function showSaveStatus(isSuccess, errorMsg) {
    // 检查是否已存在状态提示元素
    let statusEl = document.getElementById('apiKeySaveStatus');
    if (!statusEl) {
        // 创建状态提示元素
        statusEl = document.createElement('div');
        statusEl.id = 'apiKeySaveStatus';
        statusEl.className = 'text-sm mt-1 transition-opacity duration-300';
        
        // 插入到DOM中
        const parentEl = saveApiKeyCheckbox.closest('div');
        if (parentEl) {
            parentEl.appendChild(statusEl);
        }
    }
    
    // 设置状态信息
    if (isSuccess) {
        statusEl.textContent = 'API Key已保存到本地';
        statusEl.className = 'text-sm mt-1 text-green-600 transition-opacity duration-300';
    } else {
        statusEl.textContent = errorMsg ? `保存失败: ${errorMsg}` : 'API Key未保存';
        statusEl.className = 'text-sm mt-1 text-red-500 transition-opacity duration-300';
    }
    
    // 显示状态提示
    statusEl.style.opacity = '1';
    
    // 3秒后淡出
    setTimeout(() => {
        statusEl.style.opacity = '0';
    }, 3000);
}

// 设置拖拽上传功能
function setupDragAndDrop() {
    const dropZone = document.querySelector('.border-dashed');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropZone.classList.add('border-blue-500');
        dropZone.classList.remove('border-gray-300');
    }
    
    function unhighlight() {
        dropZone.classList.remove('border-blue-500');
        dropZone.classList.add('border-gray-300');
    }
    
    dropZone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    }
}

// 处理文件选择
function handleFileSelect() {
    const file = fileInput.files[0];
    if (!file) return;
    
    // 显示文件信息
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.classList.remove('hidden');
    
    // 读取Excel文件
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const excelData = new Uint8Array(e.target.result);
            workbook = XLSX.read(excelData, { type: 'array' });
            
            // 获取第一个工作表
            const firstSheetName = workbook.SheetNames[0];
            worksheet = workbook.Sheets[firstSheetName];
            
            // 转换为JSON
            data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            if (data.length > 0) {
                headers = data[0];
                validateForm();
            } else {
                showError('Excel文件没有数据');
            }
        } catch (error) {
            showError('无法读取Excel文件: ' + error.message);
        }
    };
    reader.readAsArrayBuffer(file);
}

// 验证表单
function validateForm() {
    const apiKey = apiKeyInput.value.trim();
    nextBtn.disabled = !fileInput.files[0] || !apiKey;
}

// 显示错误信息
function showError(message) {
    alert(message);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 切换到步骤2
function goToStep2() {
    step1.classList.add('hidden');
    step2.classList.remove('hidden');
    step1Indicator.classList.remove('bg-blue-500');
    step1Indicator.classList.add('bg-green-500');
    step2Indicator.classList.remove('bg-gray-300');
    step2Indicator.classList.add('bg-blue-500');
    
    // 预览数据
    previewData();
    
    // 设置列选择器
    populateColumnSelectors();
}

// 切换到步骤1
function goToStep1() {
    step2.classList.add('hidden');
    step1.classList.remove('hidden');
    step2Indicator.classList.remove('bg-blue-500');
    step2Indicator.classList.add('bg-gray-300');
    step1Indicator.classList.remove('bg-green-500');
    step1Indicator.classList.add('bg-blue-500');
}

// 预览数据
function previewData() {
    const tableHeader = document.getElementById('tableHeader');
    const tableBody = document.getElementById('tableBody');
    
    // 清空表格
    tableHeader.innerHTML = '';
    tableBody.innerHTML = '';
    
    // 添加表头
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        th.className = 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';
        tableHeader.appendChild(th);
    });
    
    // 添加数据行 (最多显示5行)
    const rowsToShow = Math.min(5, data.length - 1);
    for (let i = 1; i <= rowsToShow; i++) {
        const row = data[i];
        const tr = document.createElement('tr');
        
        row.forEach((cell, index) => {
            const td = document.createElement('td');
            td.textContent = cell !== undefined ? cell : '';
            td.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
            tr.appendChild(td);
        });
        
        tableBody.appendChild(tr);
    }
}

// 填充列选择器
function populateColumnSelectors() {
    // 清空选择器
    longitudeColSelect.innerHTML = '';
    latitudeColSelect.innerHTML = '';
    
    // 添加选项
    headers.forEach((header, index) => {
        const option1 = document.createElement('option');
        option1.value = index;
        option1.textContent = header;
        
        const option2 = document.createElement('option');
        option2.value = index;
        option2.textContent = header;
        
        longitudeColSelect.appendChild(option1);
        latitudeColSelect.appendChild(option2);
        
        // 自动选择经纬度列
        if (header.includes('经度')) {
            longitudeColSelect.value = index;
            longitudeColIndex = index;
        }
        
        if (header.includes('纬度')) {
            latitudeColSelect.value = index;
            latitudeColIndex = index;
        }
    });
    
    // 添加选择事件
    longitudeColSelect.addEventListener('change', function() {
        longitudeColIndex = parseInt(this.value);
    });
    
    latitudeColSelect.addEventListener('change', function() {
        latitudeColIndex = parseInt(this.value);
    });
}

// 开始处理数据
async function startProcessing() {
    // 获取选项
    const apiKey = apiKeyInput.value.trim();
    const isTestMode = testModeCheckbox.checked;
    
    // 验证选择
    if (longitudeColIndex === -1 || latitudeColIndex === -1) {
        showError('请选择经度和纬度列');
        return;
    }
    
    // 重置请求队列
    RequestQueue.reset();
    
    // 显示处理界面
    step2.classList.add('hidden');
    processing.classList.remove('hidden');
    
    // 准备处理数据 - 保留原始数据
    resultData = JSON.parse(JSON.stringify(data));
    
    // 添加状态列
    resultData[0].push('处理状态');
    
    // 创建新的工作表数据 - 用于存储详细地址信息
    let detailData = [];
    
    // 创建详细信息表头
    const detailHeaders = [
        '原始行号', 
        'POI ID', 'POI名称', 'POI类型', 'POI电话', 'POI距离',
        '经度', '纬度', '格式化地址',
        '国家', '省份', '城市', '区县', '城市编码', '区域编码',
        '乡镇街道', '乡镇街道编码', '社区名称', '社区类型',
        '建筑物名称', '建筑物类型', '街道名称', '门牌号',
        '方位', '距离', '所属海域', '商圈信息'
    ];
    detailData.push(detailHeaders);
    
    // 确定要处理的行数
    const startRow = 1; // 跳过表头
    const endRow = isTestMode ? Math.min(6, resultData.length) : resultData.length;
    const totalRows = endRow - startRow;
    
    // 批处理配置
    const batchSize = 2; // 每批处理的行数
    const batchDelay = 1000; // 批次间延迟(毫秒)
    
    // 重置处理统计
    processStats = { success: 0, failure: 0, skipped: 0 };
    
    try {
        // 分批处理数据
        for (let batchStart = startRow; batchStart < endRow; batchStart += batchSize) {
            const batchEnd = Math.min(batchStart + batchSize, endRow);
            const batchPromises = [];
            
            // 创建当前批次的所有请求
            for (let i = batchStart; i < batchEnd; i++) {
                const row = resultData[i];
                const longitude = row[longitudeColIndex];
                const latitude = row[latitudeColIndex];
                
                // 检查经纬度是否有效
                if (!isValidCoordinate(longitude, latitude)) {
                    // 标记为跳过
                    row.push('已跳过 (无效经纬度)');
                    processStats.skipped++;
                    continue;
                }
                
                // 添加处理Promise到批次
                batchPromises.push(processRow(i, row, longitude, latitude, apiKey, detailData));
            }
            
            // 等待当前批次的所有请求完成
            if (batchPromises.length > 0) {
                await Promise.allSettled(batchPromises);
                
                // 更新进度
                const progress = Math.round(((batchEnd - startRow) / totalRows) * 100);
                updateProgress(
                    progress, 
                    `已处理 ${batchEnd - startRow} 行，共 ${totalRows} 行 (成功: ${processStats.success}, 失败: ${processStats.failure}, 跳过: ${processStats.skipped})`
                );
                
                // 如果还有更多批次要处理，添加延迟
                if (batchEnd < endRow) {
                    await new Promise(resolve => setTimeout(resolve, batchDelay));
                }
            }
        }
        
        // 将详细数据保存到全局变量，供下载使用
        window.detailData = detailData;
        
        // 处理完成，显示结果
        updateProgress(
            100, 
            `处理完成 (成功: ${processStats.success}, 失败: ${processStats.failure}, 跳过: ${processStats.skipped})`
        );
    } catch (error) {
        updateProgress(
            100, 
            `处理中断 (成功: ${processStats.success}, 失败: ${processStats.failure}, 跳过: ${processStats.skipped})`
        );
    } finally {
        // 无论成功还是失败，都显示结果
        setTimeout(() => {
            processing.classList.add('hidden');
            showResults();
        }, 500);
    }
}

// 检查坐标是否有效
function isValidCoordinate(longitude, latitude) {
    return !(longitude === undefined || latitude === undefined || 
             longitude === '' || latitude === '' || 
             isNaN(parseFloat(longitude)) || isNaN(parseFloat(latitude)));
}

// 处理单行数据
async function processRow(rowIndex, row, longitude, latitude, apiKey, detailData) {
    try {
        // 调用API获取地址信息
        const geoInfo = await getAddressFromLocation(longitude, latitude, apiKey);
        
        // 添加状态到原始数据
        row.push('成功');
        processStats.success++;
        
        // 提取地址组件
        const addrComp = geoInfo.addressComponent || {};
        
        // 处理city字段 - 如果是数组则转为字符串
        let city = addrComp.city || '';
        if (Array.isArray(city) && city.length === 0) {
            city = '';
        }
        
        // 提取社区信息
        const neighborhood = addrComp.neighborhood || {};
        const neighborhoodName = neighborhood.name || '';
        const neighborhoodType = neighborhood.type || '';
        
        // 提取建筑物信息
        const building = addrComp.building || {};
        const buildingName = building.name || '';
        const buildingType = building.type || '';
        
        // 提取门牌信息
        const streetNumber = addrComp.streetNumber || {};
        const street = streetNumber.street || '';
        const number = streetNumber.number || '';
        const direction = streetNumber.direction || '';
        const distance = streetNumber.distance || '';
        
        // 提取商圈信息
        let businessAreas = '';
        if (addrComp.businessAreas && addrComp.businessAreas.length > 0 && !Array.isArray(addrComp.businessAreas[0])) {
            businessAreas = addrComp.businessAreas.map(area => area.name || '').join(', ');
        }
        
        // 提取POI信息
        let poiInfo = { id: '', name: '', type: '', tel: '', distance: '' };
        
        // 检查是否有POI信息
        if (geoInfo.pois && geoInfo.pois.length > 0) {
            const nearestPoi = geoInfo.pois[0]; // 获取最近的POI
            poiInfo = {
                id: nearestPoi.id || '',
                name: nearestPoi.name || '',
                type: nearestPoi.type || '',
                tel: nearestPoi.tel || '',
                distance: nearestPoi.distance || ''
            };
        }
        
        // 创建详细信息行
        const detailRow = [
            rowIndex, // 原始行号
            poiInfo.id,
            poiInfo.name,
            poiInfo.type,
            poiInfo.tel,
            poiInfo.distance,
            longitude,
            latitude,
            geoInfo.formatted_address || '',
            addrComp.country || '',
            addrComp.province || '',
            city,
            addrComp.district || '',
            addrComp.citycode || '',
            addrComp.adcode || '',
            addrComp.township || '',
            addrComp.towncode || '',
            neighborhoodName,
            neighborhoodType,
            buildingName,
            buildingType,
            street,
            number,
            direction,
            distance,
            addrComp.seaArea || '',
            businessAreas
        ];
        
        // 添加到详细数据表
        detailData.push(detailRow);
        
        return { success: true, rowIndex };
    } catch (error) {
        // 添加错误状态到原始数据
        row.push(`失败 (${error.message})`);
        processStats.failure++;
        return { success: false, rowIndex, error };
    }
}

// 更新进度条
function updateProgress(percent, status) {
    progressBar.style.width = percent + '%';
    progressText.textContent = percent + '%';
    processingStatus.textContent = status;
}

// 从经纬度获取地址信息
async function getAddressFromLocation(longitude, latitude, apiKey) {
    // 重试配置
    const maxRetries = 3;
    const initialBackoffDelay = 1000;
    const timeoutDuration = 10000;
    
    // 判断错误是否可以重试
    function isRetryableError(errorMsg) {
        if (!errorMsg) return true;
        
        const retryableErrors = [
            'ECONNRESET',
            'ETIMEDOUT',
            'ECONNREFUSED',
            'EPIPE',
            'EHOSTUNREACH',
            'EAI_AGAIN',
            'socket hang up',
            '429',
            'too many requests',
            'timeout',
            'network error',
            '请求超限'
        ];
        
        return retryableErrors.some(errText => 
            errorMsg.toLowerCase().includes(errText.toLowerCase())
        );
    }
    
    // 计算退避时间
    function calculateBackoff(retryCount) {
        return Math.min(
            initialBackoffDelay * Math.pow(2, retryCount),
            10000 // 最大10秒
        );
    }
    
    // 带重试的请求函数
    async function fetchWithRetry(retryCount = 0) {
        try {
            // 构建请求URL
            const url = `${GEO_URL}?key=${apiKey}&location=${longitude},${latitude}&radius=1000&extensions=all`;
            
            // 使用RequestQueue进行请求
            const response = await RequestQueue.enqueue(async () => {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);
                
                try {
                    const resp = await fetch(url, { signal: controller.signal });
                    clearTimeout(timeoutId);
                    return resp;
                } catch (error) {
                    clearTimeout(timeoutId);
                    throw error;
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status !== '1') {
                throw new Error(`API错误: ${data.info || '未知错误'}`);
            }
            
            if (!data.regeocode) {
                throw new Error('API返回数据格式错误');
            }
            
            return data.regeocode;
        } catch (error) {
            // 处理错误
            const errorMsg = error.message || '未知错误';
            
            // 判断是否可以重试
            if (retryCount < maxRetries && isRetryableError(errorMsg)) {
                // 计算退避时间
                const backoffTime = calculateBackoff(retryCount);
                
                // 等待退避时间
                await new Promise(resolve => setTimeout(resolve, backoffTime));
                
                // 重试请求
                return fetchWithRetry(retryCount + 1);
            }
            
            // 超过重试次数或不可重试的错误
            throw new Error(`获取地址信息失败: ${errorMsg}`);
        }
    }
    
    // 执行请求
    return await fetchWithRetry();
}

// 显示处理结果
function showResults() {
    // 更新UI状态
    step2.classList.add('hidden');
    step3.classList.remove('hidden');
    step3Indicator.classList.remove('bg-gray-300');
    step3Indicator.classList.add('bg-green-500');
    
    // 更新结果摘要
    resultSummary.textContent = `共处理 ${resultData.length - 1} 条数据，成功: ${processStats.success}，失败: ${processStats.failure}，跳过: ${processStats.skipped}`;
    
    // 获取表头和表体元素
    const resultTableHeader = document.getElementById('resultTableHeader');
    const resultTableBody = document.getElementById('resultTableBody');
    
    // 清空表格
    resultTableHeader.innerHTML = '';
    resultTableBody.innerHTML = '';
    
    // 限制显示的行数，避免DOM过大
    const maxDisplayRows = 5; // 修改为只显示5行
    const displayRows = Math.min(resultData.length, maxDisplayRows + 1); // +1 for header
    
    // 添加表头
    if (resultData.length > 0) {
        const headers = resultData[0];
        
        // 移除重复的处理状态列（如果有）
        const statusColumnIndices = [];
        headers.forEach((header, index) => {
            if (header === '处理状态') {
                statusColumnIndices.push(index);
            }
        });
        
        // 如果有多个处理状态列，只保留最后一个
        if (statusColumnIndices.length > 1) {
            const keepIndex = statusColumnIndices[statusColumnIndices.length - 1];
            
            // 从后向前删除，避免索引变化
            for (let i = statusColumnIndices.length - 2; i >= 0; i--) {
                const removeIndex = statusColumnIndices[i];
                
                // 从所有行中移除这一列
                for (let rowIndex = 0; rowIndex < resultData.length; rowIndex++) {
                    resultData[rowIndex].splice(removeIndex, 1);
                }
            }
        }
        
        // 现在添加表头
        resultData[0].forEach((header, index) => {
            const th = document.createElement('th');
            th.className = 'px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';
            th.textContent = header;
            resultTableHeader.appendChild(th);
        });
        
        // 添加数据行
        for (let i = 1; i < displayRows; i++) {
            const row = resultData[i];
            const tr = document.createElement('tr');
            
            // 添加单元格
            row.forEach((cell, cellIndex) => {
                const td = document.createElement('td');
                td.className = getCellClassName(cell, cellIndex, row.length);
                td.textContent = cell || '';
                tr.appendChild(td);
            });
            
            resultTableBody.appendChild(tr);
        }
        
        // 如果有更多行未显示，添加提示
        if (resultData.length > maxDisplayRows + 1) {
            const tr = document.createElement('tr');
            const td = document.createElement('td');
            td.colSpan = resultData[0].length;
            td.className = 'px-3 py-4 text-center text-sm text-gray-500';
            td.textContent = `还有 ${resultData.length - maxDisplayRows - 1} 行未显示，完整数据将包含在导出文件中`;
            tr.appendChild(td);
            resultTableBody.appendChild(tr);
        }
    }
    
    // 启用下载按钮
    downloadBtn.disabled = false;
}

// 获取单元格的样式类名
function getCellClassName(cell, index, rowLength) {
    if (index === rowLength - 1) {
        // 处理状态列
        if (cell === '成功') {
            return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600';
        } else if (cell && typeof cell === 'string' && cell.startsWith('失败')) {
            return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600';
        } else if (cell && typeof cell === 'string' && cell.startsWith('已跳过')) {
            return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-yellow-600';
        }
    } else if (index >= rowLength - 4 && index < rowLength - 1) {
        // 省市区列
        return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600';
    }
    
    // 原始数据列
    return 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
}

// 下载结果
function downloadResult() {
    try {
        // 创建新的工作簿
        const newWb = XLSX.utils.book_new();
        
        // 创建原始数据工作表
        const originalWs = XLSX.utils.json_to_sheet(
            resultData.slice(1).map(row => {
                const obj = {};
                resultData[0].forEach((header, index) => {
                    obj[header] = row[index];
                });
                return obj;
            })
        );
        
        // 将原始数据添加到工作簿
        XLSX.utils.book_append_sheet(newWb, originalWs, '原始数据');
        
        // 检查是否有详细数据
        if (window.detailData && window.detailData.length > 1) {
            // 创建详细信息工作表
            const detailWs = XLSX.utils.aoa_to_sheet(window.detailData);
            
            // 将详细信息添加到工作簿
            XLSX.utils.book_append_sheet(newWb, detailWs, '详细地址信息');
        }
        
        // 生成文件名
        const originalFileName = fileName.textContent;
        const baseName = originalFileName.replace(/\.[^/.]+$/, '');
        const newFileName = `${baseName}_结果.xlsx`;
        
        // 下载文件
        XLSX.writeFile(newWb, newFileName);
        
        // 记录成功信息到控制台
        console.log(`成功导出文件 ${newFileName}，包含 ${processStats.success} 条成功记录`);
    } catch (error) {
        showError('下载结果失败: ' + error.message);
    }
}

// 重置应用
function resetApp() {
    // 重置全局变量
    workbook = null;
    worksheet = null;
    headers = [];
    data = [];
    resultData = [];
    longitudeColIndex = -1;
    latitudeColIndex = -1;
    
    // 重置请求队列
    RequestQueue.reset();
    
    // 重置文件输入
    fileInput.value = '';
    fileInfo.classList.add('hidden');
    
    // 重置UI
    step3.classList.add('hidden');
    step1.classList.remove('hidden');
    step3Indicator.classList.remove('bg-blue-500');
    step3Indicator.classList.add('bg-gray-300');
    step2Indicator.classList.remove('bg-green-500');
    step2Indicator.classList.add('bg-gray-300');
    step1Indicator.classList.remove('bg-green-500');
    step1Indicator.classList.add('bg-blue-500');
    
    // 禁用下一步按钮
    nextBtn.disabled = true;
    
    // 重置测试模式
    testModeCheckbox.checked = false;
    
    // 注意：不重置API Key，保持用户输入的值
}

// 切换API Key显示/隐藏
function toggleApiKeyVisibility() {
    if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        eyeIcon.classList.add('hidden');
        eyeOffIcon.classList.remove('hidden');
    } else {
        apiKeyInput.type = 'password';
        eyeIcon.classList.remove('hidden');
        eyeOffIcon.classList.add('hidden');
    }
} 