/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，封装高德地图API调用逻辑
 * 
 * API服务 - 封装高德地图API调用
 * 提供地理编码功能，处理请求和响应
 */

import { BaseService } from '../core/BaseService.js';
import { RequestQueue } from './RequestQueue.js';
import { GeocodeResult, Coordinate } from '../models/DataModels.js';

class ApiService extends BaseService {
    constructor(apiKey = '') {
        super('ApiService');
        this.apiKey = apiKey;
        this.requestQueue = null;
        this.baseUrl = this.config.get('api.geoUrl');
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        // 初始化请求队列
        this.requestQueue = new RequestQueue();
        await this.requestQueue.initialize();
        
        // 监听请求队列事件
        this.requestQueue.on('requestCompleted', (data) => {
            this.emit('requestCompleted', data);
        });
        
        this.requestQueue.on('requestFailed', (data) => {
            this.emit('requestFailed', data);
        });

        this.logger.info('API服务初始化完成');
    }

    /**
     * 设置API密钥
     * @param {string} apiKey - API密钥
     */
    setApiKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            throw new Error('API密钥不能为空');
        }
        
        this.apiKey = apiKey.trim();
        this.logger.info('API密钥已更新');
        this.emit('apiKeyUpdated', { apiKey: this.apiKey });
    }

    /**
     * 验证API密钥格式
     * @param {string} apiKey - API密钥
     * @returns {boolean} 是否有效
     */
    validateApiKey(apiKey) {
        // 基础格式验证
        return apiKey && typeof apiKey === 'string' && apiKey.trim().length > 0;
    }

    /**
     * 根据坐标获取地址信息
     * @param {number|string} longitude - 经度
     * @param {number|string} latitude - 纬度
     * @param {Object} options - 请求选项
     * @returns {Promise<GeocodeResult>} 地理编码结果
     */
    async getAddressFromLocation(longitude, latitude, options = {}) {
        this.checkInitialized();
        
        if (!this.apiKey) {
            throw new Error('API密钥未设置');
        }

        // 验证坐标
        const coordinate = new Coordinate(longitude, latitude);
        
        const requestOptions = {
            priority: options.priority || 0,
            timeout: options.timeout || this.config.get('api.timeout'),
            maxRetries: options.maxRetries || this.config.get('api.retryCount')
        };

        // 使用请求队列执行API调用
        const response = await this.requestQueue.enqueue(
            (signal) => this.makeApiRequest(coordinate, signal),
            requestOptions
        );

        return this.parseApiResponse(response);
    }

    /**
     * 执行API请求
     * @param {Coordinate} coordinate - 坐标对象
     * @param {AbortSignal} signal - 取消信号
     * @returns {Promise<Response>} 响应对象
     */
    async makeApiRequest(coordinate, signal) {
        const url = this.buildRequestUrl(coordinate);
        
        this.logger.debug(`发起API请求: ${url}`);
        
        const response = await fetch(url, { 
            signal,
            headers: {
                'User-Agent': 'GeocodingTool/1.0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        return response;
    }

    /**
     * 构建请求URL
     * @param {Coordinate} coordinate - 坐标对象
     * @returns {string} 请求URL
     */
    buildRequestUrl(coordinate) {
        const params = new URLSearchParams({
            key: this.apiKey,
            location: coordinate.toString(),
            radius: '1000',
            extensions: 'all',
            batch: 'false',
            roadlevel: '0'
        });

        return `${this.baseUrl}?${params.toString()}`;
    }

    /**
     * 解析API响应
     * @param {Response} response - 响应对象
     * @returns {Promise<GeocodeResult>} 解析后的结果
     */
    async parseApiResponse(response) {
        const data = await response.json();
        
        this.logger.debug('API响应:', data);

        // 检查API响应状态
        if (data.status !== '1') {
            const errorMessage = this.getErrorMessage(data.infocode, data.info);
            throw new Error(`API错误: ${errorMessage}`);
        }

        // 检查是否有结果
        if (!data.regeocode) {
            throw new Error('API返回数据格式错误：缺少regeocode字段');
        }

        return new GeocodeResult(data.regeocode);
    }

    /**
     * 获取错误信息
     * @param {string} infocode - 错误代码
     * @param {string} info - 错误信息
     * @returns {string} 格式化的错误信息
     */
    getErrorMessage(infocode, info) {
        const errorMessages = {
            '10000': 'API密钥不正确或过期',
            '10001': '用户请求过于频繁',
            '10002': '请求内容有误',
            '10003': '访问已超出日访问量',
            '10004': '用户访问过于频繁',
            '10005': 'IP白名单出错，发送请求的服务器IP不在IP白名单内',
            '10006': '绑定域名出错，发送请求的域名不在安全域名内',
            '10007': '数字签名未通过验证',
            '10008': 'MD5安全码未通过验证',
            '10009': '请求key与绑定平台不符',
            '10010': 'IP访问超限',
            '10011': '服务不支持https请求',
            '10012': '权限不足，服务请求被拒绝',
            '10013': 'Key被删除',
            '20000': '请求参数非法',
            '20001': '缺少必填参数',
            '20002': '请求协议非法',
            '20003': '其他未知错误'
        };

        return errorMessages[infocode] || info || '未知错误';
    }

    /**
     * 批量获取地址信息
     * @param {Array<{longitude: number, latitude: number}>} coordinates - 坐标数组
     * @param {Object} options - 请求选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Array<GeocodeResult>>} 结果数组
     */
    async batchGetAddresses(coordinates, options = {}, onProgress = null) {
        this.checkInitialized();
        
        const results = [];
        const total = coordinates.length;
        
        this.logger.info(`开始批量处理 ${total} 个坐标`);
        
        for (let i = 0; i < coordinates.length; i++) {
            const coord = coordinates[i];
            
            try {
                const result = await this.getAddressFromLocation(
                    coord.longitude, 
                    coord.latitude, 
                    options
                );
                
                results.push({
                    index: i,
                    success: true,
                    result: result,
                    error: null
                });
                
                this.logger.debug(`处理完成 ${i + 1}/${total}`);
                
            } catch (error) {
                results.push({
                    index: i,
                    success: false,
                    result: null,
                    error: error.message
                });
                
                this.logger.warn(`处理失败 ${i + 1}/${total}:`, error.message);
            }
            
            // 调用进度回调
            if (onProgress && typeof onProgress === 'function') {
                onProgress({
                    current: i + 1,
                    total: total,
                    progress: ((i + 1) / total * 100).toFixed(2)
                });
            }
        }
        
        this.logger.info(`批量处理完成，成功: ${results.filter(r => r.success).length}，失败: ${results.filter(r => !r.success).length}`);
        
        return results;
    }

    /**
     * 测试API连接
     * @returns {Promise<boolean>} 连接是否成功
     */
    async testConnection() {
        if (!this.apiKey) {
            throw new Error('API密钥未设置');
        }

        try {
            // 使用北京天安门的坐标进行测试
            await this.getAddressFromLocation(116.397428, 39.90923);
            this.logger.info('API连接测试成功');
            return true;
        } catch (error) {
            this.logger.error('API连接测试失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取请求队列状态
     * @returns {Object} 队列状态
     */
    getQueueStatus() {
        return this.requestQueue ? this.requestQueue.getStatus() : null;
    }

    /**
     * 清空请求队列
     */
    clearQueue() {
        if (this.requestQueue) {
            this.requestQueue.clear();
            this.logger.info('请求队列已清空');
        }
    }

    /**
     * 销毁服务
     */
    async onDestroy() {
        if (this.requestQueue) {
            await this.requestQueue.destroy();
            this.requestQueue = null;
        }
        
        this.apiKey = '';
        this.logger.info('API服务已销毁');
    }
}

export { ApiService };
