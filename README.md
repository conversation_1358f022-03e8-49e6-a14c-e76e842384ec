# 经纬度逆向工具 (Web版)

这是一个基于Web的工具，用于将Excel文件中的经纬度坐标转换为省市区信息。该工具使用高德地图API进行地理编码转换，支持批量处理数据。

## 功能特点

- 支持Excel文件(.xlsx, .xls)上传和处理
- 自动识别经纬度列
- 支持拖放文件上传
- 实时显示处理进度
- 结果预览和下载
- 响应式设计，适配各种设备
- 优化的请求队列管理，避免API请求过载
- 智能错误处理和重试机制

## 技术栈

- HTML5
- TailwindCSS 3.0+ (本地化)
- JavaScript (原生)
- SheetJS (xlsx.js) 用于Excel处理 (本地化)
- 高德地图API 用于地理编码

## 使用方法

### 开发运行

1. 克隆或下载本仓库
2. 在命令行中进入项目目录
3. 运行以下命令启动本地服务器：

```bash
# 使用Python直接启动
python server.py

# 或使用npm脚本
npm start
# 或
npm run dev
```

4. 浏览器将自动打开 http://localhost:8000

### 构建部署

项目提供了完整的构建脚本：

```bash
# 构建项目到dist目录
npm run build

# 清理dist目录
npm run clean

# 清理并重新构建
npm run rebuild

# 构建并部署到GitHub Pages
npm run deploy:gh-pages
```

### 使用步骤

1. **上传文件**
   - 点击上传区域或拖放Excel文件
   - 输入高德地图API Key (默认已提供一个Key)
   - 点击"下一步"

2. **数据处理**
   - 预览数据并确认经纬度列
   - 可选择"测试模式"仅处理前5条数据
   - 点击"开始处理"

3. **下载结果**
   - 查看处理结果预览
   - 点击"下载结果"保存处理后的Excel文件

## API密钥

本工具使用高德地图API进行地理编码。默认提供了一个API Key，但如果遇到请求限制，您可以：

1. 访问[高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取新的API Key
4. 在工具中替换默认的API Key

## 代码优化

最近的代码优化包括：

- 移除了冗余的日志输出系统，减少不必要的HTTP请求
- 重构请求队列管理器，提高并发请求效率
- 优化错误处理和重试逻辑，增强程序稳定性
- 提取重复代码为独立函数，提高代码复用性
- 优化DOM操作，减少重绘和回流
- 简化服务器代码，提高可读性和可维护性
- 限制结果预览中显示的行数，提高大数据集的处理性能
- **CDN资源本地化**: 将TailwindCSS和XLSX.js库下载到本地，提高加载速度和离线可用性

## 本地化资源

项目已将所有CDN资源本地化，包括：

- **TailwindCSS**: 存储在 `lib/tailwindcss.js`
- **XLSX.js**: 存储在 `lib/xlsx.full.min.js`

### 本地化的优势

- ✅ **更快的加载速度**: 无需从外部CDN下载资源
- ✅ **离线可用**: 在没有网络连接时仍可正常使用界面
- ✅ **更高的可靠性**: 不依赖外部CDN的可用性
- ✅ **隐私保护**: 减少对第三方服务的依赖

### 测试本地化

运行 `test-local-libs.html` 文件可以验证本地资源是否正常工作。

## 注意事项

- 为避免API请求过于频繁，工具会在处理大量数据时添加适当延迟
- 处理大文件可能需要较长时间，请耐心等待
- 建议先使用测试模式验证结果再处理全部数据

## 隐私说明

所有数据处理均在本地完成，不会上传到任何服务器（除了发送给高德地图API的经纬度数据）。 