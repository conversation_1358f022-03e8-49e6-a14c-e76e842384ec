/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，实现配置管理功能
 * 
 * 配置管理器 - 统一管理应用配置
 * 使用单例模式确保全局配置的一致性
 */

class ConfigManager {
    constructor() {
        if (ConfigManager.instance) {
            return ConfigManager.instance;
        }

        this.config = new Map();
        this.initDefaultConfig();
        ConfigManager.instance = this;
    }

    /**
     * 初始化默认配置
     */
    initDefaultConfig() {
        // API相关配置
        this.config.set('api', {
            geoUrl: 'https://restapi.amap.com/v3/geocode/regeo',
            timeout: 10000,
            retryCount: 3,
            retryDelay: 1000
        });

        // 请求队列配置
        this.config.set('requestQueue', {
            maxConcurrent: 3,
            requestInterval: 300
        });

        // 存储配置
        this.config.set('storage', {
            apiKeyStorageKey: 'geoApiKey',
            prefix: 'geocoding_tool_'
        });

        // UI配置
        this.config.set('ui', {
            maxPreviewRows: 100,
            progressUpdateInterval: 100
        });

        // 文件处理配置
        this.config.set('file', {
            supportedFormats: ['.xlsx', '.xls'],
            maxFileSize: 50 * 1024 * 1024, // 50MB
            encoding: 'utf-8'
        });

        // 数据处理配置
        this.config.set('dataProcessing', {
            batchSize: 100,
            coordinateValidation: {
                longitudeRange: [-180, 180],
                latitudeRange: [-90, 90]
            }
        });
    }

    /**
     * 获取配置值
     * @param {string} key - 配置键，支持点号分隔的嵌套键
     * @param {any} defaultValue - 默认值
     * @returns {any} 配置值
     */
    get(key, defaultValue = null) {
        const keys = key.split('.');
        let value = this.config;

        for (const k of keys) {
            if (value instanceof Map) {
                value = value.get(k);
            } else if (value && typeof value === 'object') {
                value = value[k];
            } else {
                return defaultValue;
            }

            if (value === undefined) {
                return defaultValue;
            }
        }

        return value;
    }

    /**
     * 设置配置值
     * @param {string} key - 配置键，支持点号分隔的嵌套键
     * @param {any} value - 配置值
     */
    set(key, value) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        let target = this.config;

        // 导航到目标对象
        for (const k of keys) {
            if (target instanceof Map) {
                if (!target.has(k)) {
                    target.set(k, {});
                }
                target = target.get(k);
            } else {
                if (!target[k]) {
                    target[k] = {};
                }
                target = target[k];
            }
        }

        // 设置值
        if (target instanceof Map) {
            target.set(lastKey, value);
        } else {
            target[lastKey] = value;
        }
    }

    /**
     * 检查配置键是否存在
     * @param {string} key - 配置键
     * @returns {boolean} 是否存在
     */
    has(key) {
        return this.get(key) !== null;
    }

    /**
     * 删除配置
     * @param {string} key - 配置键
     */
    delete(key) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        let target = this.config;

        // 导航到目标对象
        for (const k of keys) {
            if (target instanceof Map) {
                target = target.get(k);
            } else {
                target = target[k];
            }

            if (!target) {
                return false;
            }
        }

        // 删除值
        if (target instanceof Map) {
            return target.delete(lastKey);
        } else {
            delete target[lastKey];
            return true;
        }
    }

    /**
     * 批量更新配置
     * @param {Object} updates - 配置更新对象
     */
    update(updates) {
        for (const [key, value] of Object.entries(updates)) {
            this.set(key, value);
        }
    }

    /**
     * 获取所有配置
     * @returns {Object} 配置对象
     */
    getAll() {
        const result = {};
        for (const [key, value] of this.config) {
            result[key] = value;
        }
        return result;
    }

    /**
     * 重置为默认配置
     */
    reset() {
        this.config.clear();
        this.initDefaultConfig();
    }

    /**
     * 验证配置
     * @param {string} key - 配置键
     * @param {any} value - 配置值
     * @returns {boolean} 是否有效
     */
    validate(key, value) {
        // 基础验证规则
        const validationRules = {
            'api.timeout': (v) => typeof v === 'number' && v > 0,
            'api.retryCount': (v) => typeof v === 'number' && v >= 0,
            'requestQueue.maxConcurrent': (v) => typeof v === 'number' && v > 0,
            'requestQueue.requestInterval': (v) => typeof v === 'number' && v >= 0,
            'file.maxFileSize': (v) => typeof v === 'number' && v > 0
        };

        const validator = validationRules[key];
        return validator ? validator(value) : true;
    }
}

// 创建单例实例
const configManager = new ConfigManager();

export { ConfigManager, configManager };
