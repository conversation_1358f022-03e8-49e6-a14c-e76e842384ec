<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地库测试</title>
    <!-- 引入本地TailwindCSS -->
    <script src="lib/tailwindcss.js"></script>
    <!-- 引入本地xlsx.js库 -->
    <script src="lib/xlsx.full.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 class="text-2xl font-bold text-gray-800 mb-4 text-center">本地库测试</h1>
        
        <!-- TailwindCSS测试 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold text-blue-600 mb-2">TailwindCSS 测试</h2>
            <div class="bg-blue-100 border border-blue-300 text-blue-800 px-4 py-2 rounded">
                如果您看到这个蓝色的样式框，说明TailwindCSS已成功加载！
            </div>
        </div>
        
        <!-- XLSX.js测试 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold text-green-600 mb-2">XLSX.js 测试</h2>
            <button id="testXlsx" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded w-full">
                测试XLSX库
            </button>
            <div id="xlsxResult" class="mt-2 text-sm text-gray-600"></div>
        </div>
        
        <div class="text-center text-sm text-gray-500">
            <p>✅ 所有CDN资源已成功本地化</p>
        </div>
    </div>

    <script>
        // 测试XLSX库是否正常加载
        document.getElementById('testXlsx').addEventListener('click', function() {
            const resultDiv = document.getElementById('xlsxResult');
            
            try {
                // 检查XLSX对象是否存在
                if (typeof XLSX !== 'undefined') {
                    // 创建一个简单的工作簿测试
                    const wb = XLSX.utils.book_new();
                    const ws = XLSX.utils.aoa_to_sheet([['测试', '数据'], ['Hello', 'World']]);
                    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
                    
                    resultDiv.innerHTML = '<span class="text-green-600">✅ XLSX.js库加载成功！版本: ' + XLSX.version + '</span>';
                } else {
                    resultDiv.innerHTML = '<span class="text-red-600">❌ XLSX.js库未加载</span>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="text-red-600">❌ XLSX.js库测试失败: ' + error.message + '</span>';
            }
        });
        
        // 页面加载完成后自动检查库状态
        window.addEventListener('load', function() {
            console.log('TailwindCSS状态:', typeof tailwind !== 'undefined' ? '已加载' : '未加载');
            console.log('XLSX.js状态:', typeof XLSX !== 'undefined' ? '已加载 (版本: ' + XLSX.version + ')' : '未加载');
        });
    </script>
</body>
</html>
