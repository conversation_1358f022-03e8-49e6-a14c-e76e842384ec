/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，定义服务层基础抽象类
 * 
 * 基础服务类 - 所有服务类的基类
 * 提供通用的服务功能和错误处理
 */

import { EventEmitter } from './EventEmitter.js';
import { configManager } from './ConfigManager.js';

class BaseService extends EventEmitter {
    constructor(serviceName) {
        super();
        this.serviceName = serviceName || this.constructor.name;
        this.isInitialized = false;
        this.config = configManager;
        this.logger = this.createLogger();
    }

    /**
     * 创建日志记录器
     * @returns {Object} 日志记录器对象
     */
    createLogger() {
        const serviceName = this.serviceName;
        
        return {
            debug: (message, ...args) => {
                if (this.config.get('debug', false)) {
                    console.debug(`[${serviceName}] ${message}`, ...args);
                }
            },
            info: (message, ...args) => {
                console.info(`[${serviceName}] ${message}`, ...args);
            },
            warn: (message, ...args) => {
                console.warn(`[${serviceName}] ${message}`, ...args);
            },
            error: (message, ...args) => {
                console.error(`[${serviceName}] ${message}`, ...args);
                this.emit('error', { message, args, service: serviceName });
            }
        };
    }

    /**
     * 初始化服务
     * 子类应该重写此方法来实现具体的初始化逻辑
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized) {
            this.logger.warn('服务已经初始化');
            return;
        }

        try {
            this.logger.info('正在初始化服务...');
            await this.onInitialize();
            this.isInitialized = true;
            this.logger.info('服务初始化完成');
            this.emit('initialized');
        } catch (error) {
            this.logger.error('服务初始化失败:', error);
            this.emit('initializationError', error);
            throw error;
        }
    }

    /**
     * 子类重写此方法来实现具体的初始化逻辑
     * @returns {Promise<void>}
     */
    async onInitialize() {
        // 默认实现为空，子类可以重写
    }

    /**
     * 销毁服务
     * @returns {Promise<void>}
     */
    async destroy() {
        if (!this.isInitialized) {
            return;
        }

        try {
            this.logger.info('正在销毁服务...');
            await this.onDestroy();
            this.isInitialized = false;
            this.removeAllListeners();
            this.logger.info('服务销毁完成');
        } catch (error) {
            this.logger.error('服务销毁失败:', error);
            throw error;
        }
    }

    /**
     * 子类重写此方法来实现具体的销毁逻辑
     * @returns {Promise<void>}
     */
    async onDestroy() {
        // 默认实现为空，子类可以重写
    }

    /**
     * 检查服务是否已初始化
     * @returns {boolean} 是否已初始化
     */
    checkInitialized() {
        if (!this.isInitialized) {
            throw new Error(`服务 ${this.serviceName} 尚未初始化`);
        }
        return true;
    }

    /**
     * 安全执行异步操作，包含错误处理
     * @param {Function} operation - 要执行的异步操作
     * @param {string} operationName - 操作名称，用于日志
     * @returns {Promise<any>} 操作结果
     */
    async safeExecute(operation, operationName = '未知操作') {
        try {
            this.logger.debug(`开始执行: ${operationName}`);
            const result = await operation();
            this.logger.debug(`完成执行: ${operationName}`);
            return result;
        } catch (error) {
            this.logger.error(`执行失败 [${operationName}]:`, error);
            this.emit('operationError', { operationName, error });
            throw error;
        }
    }

    /**
     * 带重试的异步操作执行
     * @param {Function} operation - 要执行的异步操作
     * @param {Object} options - 重试选项
     * @param {number} options.maxRetries - 最大重试次数
     * @param {number} options.retryDelay - 重试延迟（毫秒）
     * @param {string} options.operationName - 操作名称
     * @returns {Promise<any>} 操作结果
     */
    async executeWithRetry(operation, options = {}) {
        const {
            maxRetries = this.config.get('api.retryCount', 3),
            retryDelay = this.config.get('api.retryDelay', 1000),
            operationName = '重试操作'
        } = options;

        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    this.logger.info(`重试 ${operationName} (第${attempt}次)`);
                    await this.delay(retryDelay * attempt); // 递增延迟
                }
                
                return await operation();
            } catch (error) {
                lastError = error;
                this.logger.warn(`${operationName} 失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error.message);
                
                if (attempt === maxRetries) {
                    break;
                }
            }
        }

        this.logger.error(`${operationName} 最终失败，已达到最大重试次数`);
        throw lastError;
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise<void>}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取服务状态
     * @returns {Object} 服务状态信息
     */
    getStatus() {
        return {
            serviceName: this.serviceName,
            isInitialized: this.isInitialized,
            eventListenerCount: this.eventNames().reduce((count, eventName) => {
                return count + this.listenerCount(eventName);
            }, 0)
        };
    }
}

export { BaseService };
