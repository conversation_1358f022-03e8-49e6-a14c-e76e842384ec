/**
 * 构建脚本 - 经纬度逆向工具
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，支持文件复制和路径修正
 */

const fs = require('fs');
const path = require('path');

// 递归复制目录
function copyDir(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (let entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            copyDir(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

// 主构建函数
function build() {
    console.log('🚀 开始构建项目...');
    
    // 确保dist目录存在
    if (!fs.existsSync('dist')) {
        fs.mkdirSync('dist');
        console.log('📁 创建dist目录');
    }
    
    // 复制并处理index.html
    console.log('📄 处理index.html...');
    let htmlContent = fs.readFileSync('index.html', 'utf8');
    // 确保dist版本使用正确的相对路径
    htmlContent = htmlContent.replace(/src="lib\//g, 'src="lib/');
    fs.writeFileSync(path.join('dist', 'index.html'), htmlContent);
    
    // 复制app.js
    console.log('📄 复制app.js...');
    fs.copyFileSync('app.js', path.join('dist', 'app.js'));
    
    // 复制README.md（如果存在）
    if (fs.existsSync('README.md')) {
        console.log('📄 复制README.md...');
        fs.copyFileSync('README.md', path.join('dist', 'README.md'));
    }
    
    // 复制lib目录（如果存在）
    if (fs.existsSync('lib')) {
        console.log('📚 复制lib目录...');
        copyDir('lib', path.join('dist', 'lib'));
        
        // 显示复制的库文件信息
        const libFiles = fs.readdirSync('lib');
        libFiles.forEach(file => {
            const filePath = path.join('lib', file);
            const stats = fs.statSync(filePath);
            const sizeKB = (stats.size / 1024).toFixed(2);
            console.log(`   ✅ ${file} (${sizeKB} KB)`);
        });
    }
    
    console.log('✨ 构建完成！文件已复制到dist目录');
    console.log('📦 dist目录内容:');
    
    // 显示dist目录结构
    function showDirStructure(dir, prefix = '') {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        entries.forEach((entry, index) => {
            const isLast = index === entries.length - 1;
            const currentPrefix = prefix + (isLast ? '└── ' : '├── ');
            console.log(currentPrefix + entry.name);
            
            if (entry.isDirectory()) {
                const nextPrefix = prefix + (isLast ? '    ' : '│   ');
                showDirStructure(path.join(dir, entry.name), nextPrefix);
            }
        });
    }
    
    showDirStructure('dist');
}

// 错误处理
try {
    build();
} catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
}
