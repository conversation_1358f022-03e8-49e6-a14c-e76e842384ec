/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，定义应用数据模型
 * 
 * 数据模型 - 定义应用中使用的数据结构
 * 提供数据验证和转换功能
 */

/**
 * 坐标点模型
 */
class Coordinate {
    constructor(longitude, latitude) {
        this.longitude = this.validateLongitude(longitude);
        this.latitude = this.validateLatitude(latitude);
    }

    validateLongitude(longitude) {
        const lng = parseFloat(longitude);
        if (isNaN(lng) || lng < -180 || lng > 180) {
            throw new Error(`无效的经度值: ${longitude}`);
        }
        return lng;
    }

    validateLatitude(latitude) {
        const lat = parseFloat(latitude);
        if (isNaN(lat) || lat < -90 || lat > 90) {
            throw new Error(`无效的纬度值: ${latitude}`);
        }
        return lat;
    }

    toString() {
        return `${this.longitude},${this.latitude}`;
    }

    toObject() {
        return {
            longitude: this.longitude,
            latitude: this.latitude
        };
    }
}

/**
 * 地址组件模型
 */
class AddressComponent {
    constructor(data = {}) {
        this.country = data.country || '';
        this.province = data.province || '';
        this.city = this.processCity(data.city);
        this.district = data.district || '';
        this.township = data.township || '';
        this.neighborhood = data.neighborhood || '';
        this.building = data.building || '';
        this.citycode = data.citycode || '';
        this.adcode = data.adcode || '';
        this.towncode = data.towncode || '';
        this.streetNumber = data.streetNumber || {};
    }

    processCity(city) {
        if (Array.isArray(city)) {
            return city.length > 0 ? city[0] : '';
        }
        return city || '';
    }

    toObject() {
        return {
            country: this.country,
            province: this.province,
            city: this.city,
            district: this.district,
            township: this.township,
            neighborhood: this.neighborhood,
            building: this.building,
            citycode: this.citycode,
            adcode: this.adcode,
            towncode: this.towncode,
            streetNumber: this.streetNumber
        };
    }
}

/**
 * POI信息模型
 */
class POIInfo {
    constructor(data = {}) {
        this.id = data.id || '';
        this.name = data.name || '';
        this.type = data.type || '';
        this.tel = data.tel || '';
        this.distance = data.distance || '';
        this.direction = data.direction || '';
    }

    toObject() {
        return {
            id: this.id,
            name: this.name,
            type: this.type,
            tel: this.tel,
            distance: this.distance,
            direction: this.direction
        };
    }
}

/**
 * 地理编码结果模型
 */
class GeocodeResult {
    constructor(data = {}) {
        this.formattedAddress = data.formatted_address || '';
        this.addressComponent = new AddressComponent(data.addressComponent || {});
        this.pois = (data.pois || []).map(poi => new POIInfo(poi));
        this.roads = data.roads || [];
        this.roadinters = data.roadinters || [];
        this.aois = data.aois || [];
        this.businessAreas = data.businessAreas || [];
    }

    toDetailedArray() {
        const addr = this.addressComponent;
        const poi = this.pois[0] || new POIInfo();
        
        return [
            poi.id,
            poi.name,
            poi.type,
            poi.tel,
            poi.distance,
            this.formattedAddress,
            addr.country,
            addr.province,
            addr.city,
            addr.district,
            addr.citycode,
            addr.adcode,
            addr.township,
            addr.towncode,
            addr.neighborhood.name || '',
            addr.neighborhood.type || '',
            addr.building.name || '',
            addr.building.type || '',
            addr.streetNumber.street || '',
            addr.streetNumber.number || '',
            poi.direction,
            poi.distance,
            addr.seaArea || '',
            this.businessAreas.map(ba => ba.name).join(';')
        ];
    }

    toObject() {
        return {
            formattedAddress: this.formattedAddress,
            addressComponent: this.addressComponent.toObject(),
            pois: this.pois.map(poi => poi.toObject()),
            roads: this.roads,
            roadinters: this.roadinters,
            aois: this.aois,
            businessAreas: this.businessAreas
        };
    }
}

/**
 * 数据行模型
 */
class DataRow {
    constructor(data, headers, rowIndex) {
        this.data = [...data];
        this.headers = headers;
        this.rowIndex = rowIndex;
        this.status = 'pending'; // pending, processing, success, error, skipped
        this.error = null;
        this.geocodeResult = null;
    }

    getValue(columnName) {
        const index = this.headers.indexOf(columnName);
        return index !== -1 ? this.data[index] : null;
    }

    setValue(columnName, value) {
        const index = this.headers.indexOf(columnName);
        if (index !== -1) {
            this.data[index] = value;
        }
    }

    addValue(value) {
        this.data.push(value);
    }

    setStatus(status, error = null) {
        this.status = status;
        this.error = error;
    }

    setGeocodeResult(result) {
        this.geocodeResult = result;
        this.status = 'success';
    }

    toArray() {
        return [...this.data];
    }

    toObject() {
        const obj = {};
        this.headers.forEach((header, index) => {
            obj[header] = this.data[index];
        });
        return obj;
    }
}

/**
 * 数据集模型
 */
class DataSet {
    constructor(headers = [], rows = []) {
        this.headers = [...headers];
        this.rows = rows.map((row, index) => 
            row instanceof DataRow ? row : new DataRow(row, this.headers, index)
        );
        this.metadata = {
            totalRows: this.rows.length,
            processedRows: 0,
            successRows: 0,
            errorRows: 0,
            skippedRows: 0
        };
    }

    addRow(rowData) {
        const row = new DataRow(rowData, this.headers, this.rows.length);
        this.rows.push(row);
        this.metadata.totalRows++;
        return row;
    }

    getRow(index) {
        return this.rows[index];
    }

    updateMetadata() {
        this.metadata.processedRows = this.rows.filter(row => row.status !== 'pending').length;
        this.metadata.successRows = this.rows.filter(row => row.status === 'success').length;
        this.metadata.errorRows = this.rows.filter(row => row.status === 'error').length;
        this.metadata.skippedRows = this.rows.filter(row => row.status === 'skipped').length;
    }

    toArray() {
        return [this.headers, ...this.rows.map(row => row.toArray())];
    }

    getProcessingStats() {
        this.updateMetadata();
        return { ...this.metadata };
    }
}

/**
 * 应用状态模型
 */
class AppState {
    constructor() {
        this.currentStep = 1; // 1: 文件上传, 2: 数据配置, 3: 处理中, 4: 结果展示
        this.file = null;
        this.dataSet = null;
        this.apiKey = '';
        this.isTestMode = false;
        this.longitudeColumnIndex = -1;
        this.latitudeColumnIndex = -1;
        this.processing = {
            isRunning: false,
            progress: 0,
            currentRow: 0,
            status: ''
        };
    }

    setStep(step) {
        this.currentStep = step;
    }

    setFile(file) {
        this.file = file;
    }

    setDataSet(dataSet) {
        this.dataSet = dataSet;
    }

    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }

    setTestMode(isTestMode) {
        this.isTestMode = isTestMode;
    }

    setColumnIndexes(longitudeIndex, latitudeIndex) {
        this.longitudeColumnIndex = longitudeIndex;
        this.latitudeColumnIndex = latitudeIndex;
    }

    updateProcessing(updates) {
        Object.assign(this.processing, updates);
    }

    reset() {
        this.currentStep = 1;
        this.file = null;
        this.dataSet = null;
        this.apiKey = '';
        this.isTestMode = false;
        this.longitudeColumnIndex = -1;
        this.latitudeColumnIndex = -1;
        this.processing = {
            isRunning: false,
            progress: 0,
            currentRow: 0,
            status: ''
        };
    }
}

export {
    Coordinate,
    AddressComponent,
    POIInfo,
    GeocodeResult,
    DataRow,
    DataSet,
    AppState
};
