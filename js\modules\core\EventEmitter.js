/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，实现基础事件系统
 * 
 * 事件发射器 - 实现观察者模式的基础事件系统
 * 用于模块间的松耦合通信
 */

class EventEmitter {
    constructor() {
        this.events = new Map();
    }

    /**
     * 注册事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} listener - 监听器函数
     * @param {Object} options - 选项
     * @param {boolean} options.once - 是否只执行一次
     */
    on(eventName, listener, options = {}) {
        if (typeof listener !== 'function') {
            throw new Error('监听器必须是一个函数');
        }

        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }

        const listenerWrapper = {
            listener,
            once: options.once || false
        };

        this.events.get(eventName).push(listenerWrapper);
        return this;
    }

    /**
     * 注册一次性事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} listener - 监听器函数
     */
    once(eventN<PERSON>, listener) {
        return this.on(eventName, listener, { once: true });
    }

    /**
     * 移除事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} listener - 要移除的监听器函数
     */
    off(eventName, listener) {
        if (!this.events.has(eventName)) {
            return this;
        }

        const listeners = this.events.get(eventName);
        const index = listeners.findIndex(wrapper => wrapper.listener === listener);
        
        if (index !== -1) {
            listeners.splice(index, 1);
        }

        if (listeners.length === 0) {
            this.events.delete(eventName);
        }

        return this;
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {...any} args - 传递给监听器的参数
     */
    emit(eventName, ...args) {
        if (!this.events.has(eventName)) {
            return false;
        }

        const listeners = this.events.get(eventName).slice(); // 创建副本避免在执行过程中被修改
        const onceListeners = [];

        for (const wrapper of listeners) {
            try {
                wrapper.listener.apply(this, args);
                
                if (wrapper.once) {
                    onceListeners.push(wrapper);
                }
            } catch (error) {
                console.error(`事件监听器执行错误 [${eventName}]:`, error);
            }
        }

        // 移除一次性监听器
        if (onceListeners.length > 0) {
            const remainingListeners = this.events.get(eventName)
                .filter(wrapper => !onceListeners.includes(wrapper));
            
            if (remainingListeners.length === 0) {
                this.events.delete(eventName);
            } else {
                this.events.set(eventName, remainingListeners);
            }
        }

        return true;
    }

    /**
     * 移除所有监听器
     * @param {string} [eventName] - 可选的事件名称，如果不提供则移除所有事件的监听器
     */
    removeAllListeners(eventName) {
        if (eventName) {
            this.events.delete(eventName);
        } else {
            this.events.clear();
        }
        return this;
    }

    /**
     * 获取事件的监听器数量
     * @param {string} eventName - 事件名称
     * @returns {number} 监听器数量
     */
    listenerCount(eventName) {
        return this.events.has(eventName) ? this.events.get(eventName).length : 0;
    }

    /**
     * 获取所有事件名称
     * @returns {string[]} 事件名称数组
     */
    eventNames() {
        return Array.from(this.events.keys());
    }
}

// 创建全局事件总线实例
const globalEventBus = new EventEmitter();

export { EventEmitter, globalEventBus };
