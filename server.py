import http.server
import socketserver
import webbrowser
import os

# 配置服务器
PORT = 8000
Handler = http.server.SimpleHTTPRequestHandler

# 启动服务器
def start_server():
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"服务器已启动，访问地址: http://localhost:{PORT}")
        print("按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        webbrowser.open(f"http://localhost:{PORT}")
        
        # 保持服务器运行
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    # 检查必要文件是否存在
    required_files = ["index.html", "app.js"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误: 未找到必要的文件: {', '.join(missing_files)}")
        exit(1)
        
    print("正在启动经纬度转省市区Web工具...")
    start_server() 