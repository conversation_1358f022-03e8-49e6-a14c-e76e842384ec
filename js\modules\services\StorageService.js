/**
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 初始版本，封装localStorage操作
 * 
 * 存储服务 - 封装本地存储操作
 * 提供统一的存储接口，处理存储异常
 */

import { BaseService } from '../core/BaseService.js';

class StorageService extends BaseService {
    constructor() {
        super('StorageService');
        this.isAvailable = false;
        this.prefix = this.config.get('storage.prefix', 'geocoding_tool_');
    }

    /**
     * 初始化服务
     */
    async onInitialize() {
        this.checkAvailability();
        this.logger.info(`存储服务初始化完成，可用性: ${this.isAvailable}`);
    }

    /**
     * 检查localStorage可用性
     */
    checkAvailability() {
        try {
            const testKey = this.prefix + 'test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            this.isAvailable = true;
        } catch (error) {
            this.isAvailable = false;
            this.logger.warn('localStorage不可用:', error.message);
        }
    }

    /**
     * 生成带前缀的键名
     * @param {string} key - 原始键名
     * @returns {string} 带前缀的键名
     */
    getFullKey(key) {
        return this.prefix + key;
    }

    /**
     * 存储数据
     * @param {string} key - 键名
     * @param {any} value - 值
     * @param {Object} options - 选项
     * @param {boolean} options.encrypt - 是否加密存储
     * @returns {boolean} 是否成功
     */
    setItem(key, value, options = {}) {
        if (!this.isAvailable) {
            this.logger.warn('存储服务不可用，无法保存数据');
            return false;
        }

        try {
            const fullKey = this.getFullKey(key);
            let dataToStore = value;

            // 如果值不是字符串，则序列化
            if (typeof value !== 'string') {
                dataToStore = JSON.stringify(value);
            }

            // 如果需要加密（简单的Base64编码）
            if (options.encrypt) {
                dataToStore = btoa(dataToStore);
            }

            localStorage.setItem(fullKey, dataToStore);
            
            this.logger.debug(`数据已保存: ${key}`);
            this.emit('itemStored', { key, success: true });
            
            return true;
        } catch (error) {
            this.logger.error(`保存数据失败 [${key}]:`, error.message);
            this.emit('itemStored', { key, success: false, error: error.message });
            return false;
        }
    }

    /**
     * 获取数据
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @param {Object} options - 选项
     * @param {boolean} options.decrypt - 是否解密
     * @param {boolean} options.parse - 是否解析JSON
     * @returns {any} 存储的值或默认值
     */
    getItem(key, defaultValue = null, options = {}) {
        if (!this.isAvailable) {
            this.logger.warn('存储服务不可用，返回默认值');
            return defaultValue;
        }

        try {
            const fullKey = this.getFullKey(key);
            let data = localStorage.getItem(fullKey);

            if (data === null) {
                return defaultValue;
            }

            // 如果需要解密
            if (options.decrypt) {
                try {
                    data = atob(data);
                } catch (decryptError) {
                    this.logger.warn(`解密失败 [${key}]:`, decryptError.message);
                    return defaultValue;
                }
            }

            // 如果需要解析JSON
            if (options.parse) {
                try {
                    data = JSON.parse(data);
                } catch (parseError) {
                    this.logger.warn(`JSON解析失败 [${key}]:`, parseError.message);
                    return defaultValue;
                }
            }

            this.logger.debug(`数据已读取: ${key}`);
            return data;
            
        } catch (error) {
            this.logger.error(`读取数据失败 [${key}]:`, error.message);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {boolean} 是否成功
     */
    removeItem(key) {
        if (!this.isAvailable) {
            this.logger.warn('存储服务不可用，无法删除数据');
            return false;
        }

        try {
            const fullKey = this.getFullKey(key);
            localStorage.removeItem(fullKey);
            
            this.logger.debug(`数据已删除: ${key}`);
            this.emit('itemRemoved', { key, success: true });
            
            return true;
        } catch (error) {
            this.logger.error(`删除数据失败 [${key}]:`, error.message);
            this.emit('itemRemoved', { key, success: false, error: error.message });
            return false;
        }
    }

    /**
     * 检查键是否存在
     * @param {string} key - 键名
     * @returns {boolean} 是否存在
     */
    hasItem(key) {
        if (!this.isAvailable) {
            return false;
        }

        const fullKey = this.getFullKey(key);
        return localStorage.getItem(fullKey) !== null;
    }

    /**
     * 获取所有以指定前缀开头的键
     * @param {string} keyPrefix - 键前缀
     * @returns {Array<string>} 键名数组
     */
    getKeys(keyPrefix = '') {
        if (!this.isAvailable) {
            return [];
        }

        const keys = [];
        const searchPrefix = this.getFullKey(keyPrefix);

        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(searchPrefix)) {
                    // 移除前缀返回原始键名
                    const originalKey = key.substring(this.prefix.length);
                    keys.push(originalKey);
                }
            }
        } catch (error) {
            this.logger.error('获取键列表失败:', error.message);
        }

        return keys;
    }

    /**
     * 清空所有应用相关的存储数据
     * @returns {boolean} 是否成功
     */
    clear() {
        if (!this.isAvailable) {
            this.logger.warn('存储服务不可用，无法清空数据');
            return false;
        }

        try {
            const keysToRemove = [];
            
            // 收集所有需要删除的键
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }

            // 删除收集到的键
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
            });

            this.logger.info(`已清空 ${keysToRemove.length} 个存储项`);
            this.emit('storageCleared', { count: keysToRemove.length });
            
            return true;
        } catch (error) {
            this.logger.error('清空存储失败:', error.message);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 存储使用情况
     */
    getStorageInfo() {
        if (!this.isAvailable) {
            return {
                available: false,
                totalItems: 0,
                appItems: 0,
                estimatedSize: 0
            };
        }

        try {
            let appItems = 0;
            let estimatedSize = 0;

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    appItems++;
                    const value = localStorage.getItem(key);
                    if (value) {
                        estimatedSize += key.length + value.length;
                    }
                }
            }

            return {
                available: true,
                totalItems: localStorage.length,
                appItems: appItems,
                estimatedSize: estimatedSize,
                estimatedSizeKB: (estimatedSize / 1024).toFixed(2)
            };
        } catch (error) {
            this.logger.error('获取存储信息失败:', error.message);
            return {
                available: false,
                totalItems: 0,
                appItems: 0,
                estimatedSize: 0,
                error: error.message
            };
        }
    }

    /**
     * API密钥相关的便捷方法
     */
    
    /**
     * 保存API密钥
     * @param {string} apiKey - API密钥
     * @returns {boolean} 是否成功
     */
    saveApiKey(apiKey) {
        const keyName = this.config.get('storage.apiKeyStorageKey', 'geoApiKey');
        return this.setItem(keyName, apiKey, { encrypt: true });
    }

    /**
     * 获取API密钥
     * @returns {string|null} API密钥
     */
    getApiKey() {
        const keyName = this.config.get('storage.apiKeyStorageKey', 'geoApiKey');
        return this.getItem(keyName, null, { decrypt: true });
    }

    /**
     * 删除API密钥
     * @returns {boolean} 是否成功
     */
    removeApiKey() {
        const keyName = this.config.get('storage.apiKeyStorageKey', 'geoApiKey');
        return this.removeItem(keyName);
    }

    /**
     * 检查是否有保存的API密钥
     * @returns {boolean} 是否存在
     */
    hasApiKey() {
        const keyName = this.config.get('storage.apiKeyStorageKey', 'geoApiKey');
        return this.hasItem(keyName);
    }

    /**
     * 销毁服务
     */
    async onDestroy() {
        this.isAvailable = false;
        this.logger.info('存储服务已销毁');
    }
}

export { StorageService };
